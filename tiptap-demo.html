<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tiptap 富文本编辑器演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f7;
            color: #1d1d1f;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .editor-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .toolbar {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 16px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .toolbar-group {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .toolbar-group:not(:last-child)::after {
            content: '';
            width: 1px;
            height: 24px;
            background: #dee2e6;
            margin-left: 8px;
        }

        .toolbar button, .toolbar select {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .toolbar button:hover, .toolbar select:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }

        .toolbar button.active {
            background: #007aff;
            color: white;
            border-color: #007aff;
        }

        .toolbar button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .editor {
            min-height: 400px;
            padding: 24px;
            font-size: 16px;
            line-height: 1.6;
        }

        .editor:focus {
            outline: none;
        }

        /* Tiptap 编辑器样式 */
        .tiptap {
            outline: none;
        }

        .tiptap h1, .tiptap h2, .tiptap h3 {
            margin: 1.5em 0 0.5em 0;
            font-weight: 600;
        }

        .tiptap h1 { font-size: 2rem; }
        .tiptap h2 { font-size: 1.5rem; }
        .tiptap h3 { font-size: 1.25rem; }

        .tiptap p {
            margin: 0.75em 0;
        }

        .tiptap ul, .tiptap ol {
            margin: 0.75em 0;
            padding-left: 1.5em;
        }

        .tiptap li {
            margin: 0.25em 0;
        }

        .tiptap blockquote {
            border-left: 4px solid #007aff;
            padding-left: 1em;
            margin: 1em 0;
            font-style: italic;
            color: #666;
        }

        .tiptap code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 0.9em;
        }

        .tiptap pre {
            background: #f8f9fa;
            padding: 1em;
            border-radius: 8px;
            overflow-x: auto;
            margin: 1em 0;
        }

        .tiptap pre code {
            background: none;
            padding: 0;
        }

        .tiptap a {
            color: #007aff;
            text-decoration: none;
        }

        .tiptap a:hover {
            text-decoration: underline;
        }

        .tiptap img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 1em 0;
        }

        .tiptap hr {
            border: none;
            height: 2px;
            background: #e9ecef;
            margin: 2em 0;
            border-radius: 1px;
        }

        /* 浮动菜单样式 */
        .bubble-menu {
            display: flex;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            padding: 8px;
            gap: 4px;
        }

        .bubble-menu button {
            padding: 6px 10px;
            border: none;
            background: transparent;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .bubble-menu button:hover {
            background: #f8f9fa;
        }

        .bubble-menu button.active {
            background: #007aff;
            color: white;
        }

        /* 统计信息 */
        .stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
        }

        .stats-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 功能演示区域 */
        .demo-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .demo-section h3 {
            margin-bottom: 16px;
            color: #1d1d1f;
            font-size: 1.25rem;
        }

        .demo-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .demo-button {
            padding: 10px 16px;
            background: #007aff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .demo-button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .demo-button:active {
            transform: translateY(0);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .toolbar {
                padding: 12px;
            }

            .editor {
                padding: 16px;
            }

            .stats {
                flex-direction: column;
                gap: 8px;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Tiptap 富文本编辑器</h1>
            <p>体验现代化的无头富文本编辑器框架</p>
        </div>

        <div class="editor-container">
            <div class="toolbar">
                <div class="toolbar-group">
                    <button id="undo" title="撤销">↶</button>
                    <button id="redo" title="重做">↷</button>
                </div>

                <div class="toolbar-group">
                    <select id="heading">
                        <option value="paragraph">正文</option>
                        <option value="1">标题 1</option>
                        <option value="2">标题 2</option>
                        <option value="3">标题 3</option>
                    </select>
                </div>

                <div class="toolbar-group">
                    <button id="bold" title="粗体"><strong>B</strong></button>
                    <button id="italic" title="斜体"><em>I</em></button>
                    <button id="underline" title="下划线"><u>U</u></button>
                    <button id="strike" title="删除线"><s>S</s></button>
                </div>

                <div class="toolbar-group">
                    <button id="highlight" title="高亮">🖍️</button>
                    <button id="code" title="行内代码">&lt;/&gt;</button>
                </div>

                <div class="toolbar-group">
                    <button id="bulletList" title="无序列表">• 列表</button>
                    <button id="orderedList" title="有序列表">1. 列表</button>
                    <button id="taskList" title="任务列表">☑️ 任务</button>
                </div>

                <div class="toolbar-group">
                    <button id="blockquote" title="引用">❝ 引用</button>
                    <button id="codeBlock" title="代码块">{ } 代码</button>
                    <button id="horizontalRule" title="分割线">━━━</button>
                </div>

                <div class="toolbar-group">
                    <button id="link" title="链接">🔗</button>
                    <button id="image" title="图片">🖼️</button>
                </div>

                <div class="toolbar-group">
                    <button id="textAlign" title="文本对齐">⚏</button>
                </div>
            </div>

            <div id="editor" class="editor"></div>

            <div class="stats">
                <div class="stats-item">
                    <span>字符数：</span>
                    <span id="charCount">0</span>
                </div>
                <div class="stats-item">
                    <span>单词数：</span>
                    <span id="wordCount">0</span>
                </div>
                <div class="stats-item">
                    <span>段落数：</span>
                    <span id="paragraphCount">0</span>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>📝 内容操作演示</h3>
            <div class="demo-buttons">
                <button class="demo-button" onclick="insertSampleContent()">插入示例内容</button>
                <button class="demo-button" onclick="getHTML()">获取 HTML</button>
                <button class="demo-button" onclick="getJSON()">获取 JSON</button>
                <button class="demo-button" onclick="clearContent()">清空内容</button>
                <button class="demo-button" onclick="focusEditor()">聚焦编辑器</button>
            </div>
        </div>

        <div class="demo-section">
            <h3>🎯 格式化演示</h3>
            <div class="demo-buttons">
                <button class="demo-button" onclick="insertTable()">插入表格</button>
                <button class="demo-button" onclick="insertEmoji()">插入表情</button>
                <button class="demo-button" onclick="toggleReadOnly()">切换只读模式</button>
                <button class="demo-button" onclick="selectAll()">全选内容</button>
            </div>
        </div>

        <div class="demo-section">
            <h3>📊 输出预览</h3>
            <div id="output" style="background: #f8f9fa; padding: 16px; border-radius: 8px; font-family: monospace; font-size: 14px; white-space: pre-wrap; max-height: 200px; overflow-y: auto;"></div>
        </div>
    </div>

    <!-- 使用 CDN 引入 Tiptap -->
    <script type="module">
        import { Editor } from 'https://esm.sh/@tiptap/core@2.1.13'
        import StarterKit from 'https://esm.sh/@tiptap/starter-kit@2.1.13'
        import Highlight from 'https://esm.sh/@tiptap/extension-highlight@2.1.13'
        import Underline from 'https://esm.sh/@tiptap/extension-underline@2.1.13'
        import TextAlign from 'https://esm.sh/@tiptap/extension-text-align@2.1.13'
        import Link from 'https://esm.sh/@tiptap/extension-link@2.1.13'
        import Image from 'https://esm.sh/@tiptap/extension-image@2.1.13'
        import TaskList from 'https://esm.sh/@tiptap/extension-task-list@2.1.13'
        import TaskItem from 'https://esm.sh/@tiptap/extension-task-item@2.1.13'
        import CharacterCount from 'https://esm.sh/@tiptap/extension-character-count@2.1.13'
        import Placeholder from 'https://esm.sh/@tiptap/extension-placeholder@2.1.13'

        // 全局变量
        let editor;
        let isReadOnly = false;

        // 初始化编辑器
        function initEditor() {
            editor = new Editor({
                element: document.querySelector('#editor'),
                extensions: [
                    StarterKit.configure({
                        history: {
                            depth: 100,
                        },
                    }),
                    Highlight.configure({
                        multicolor: true,
                    }),
                    Underline,
                    TextAlign.configure({
                        types: ['heading', 'paragraph'],
                    }),
                    Link.configure({
                        openOnClick: false,
                        HTMLAttributes: {
                            class: 'tiptap-link',
                        },
                    }),
                    Image.configure({
                        inline: true,
                        allowBase64: true,
                    }),
                    TaskList,
                    TaskItem.configure({
                        nested: true,
                    }),
                    CharacterCount,
                    Placeholder.configure({
                        placeholder: '开始输入你的内容...',
                    }),
                ],
                content: `
                    <h1>欢迎使用 Tiptap 编辑器！</h1>
                    <p>这是一个功能强大的<strong>富文本编辑器</strong>演示。你可以：</p>
                    <ul>
                        <li>使用各种<em>文本格式</em></li>
                        <li>插入<a href="https://tiptap.dev">链接</a></li>
                        <li>添加<mark>高亮文本</mark></li>
                        <li>创建代码块和<code>行内代码</code></li>
                    </ul>
                    <blockquote>
                        <p>Tiptap 是一个无头的富文本编辑器框架，基于 ProseMirror 构建。</p>
                    </blockquote>
                    <p>试试上面的工具栏按钮，或者选择文本查看浮动菜单！</p>
                `,
                onTransaction: () => {
                    updateUI();
                    updateStats();
                },
                onSelectionUpdate: ({ editor }) => {
                    updateToolbarStates();
                },
            });

            // 创建浮动菜单
            createBubbleMenu();

            // 初始化工具栏事件
            initToolbarEvents();

            // 初始化 UI 状态
            updateUI();
            updateStats();
        }

        // 创建浮动菜单
        function createBubbleMenu() {
            const bubbleMenu = document.createElement('div');
            bubbleMenu.className = 'bubble-menu';
            bubbleMenu.innerHTML = `
                <button data-action="bold" title="粗体"><strong>B</strong></button>
                <button data-action="italic" title="斜体"><em>I</em></button>
                <button data-action="underline" title="下划线"><u>U</u></button>
                <button data-action="highlight" title="高亮">🖍️</button>
                <button data-action="link" title="链接">🔗</button>
            `;

            document.body.appendChild(bubbleMenu);

            // 浮动菜单事件
            bubbleMenu.addEventListener('click', (e) => {
                const button = e.target.closest('button');
                if (!button) return;

                const action = button.dataset.action;
                handleBubbleMenuAction(action);
            });

            // 监听选择变化来显示/隐藏浮动菜单
            let hideTimeout;
            editor.on('selectionUpdate', ({ editor }) => {
                clearTimeout(hideTimeout);

                if (editor.state.selection.empty) {
                    bubbleMenu.style.display = 'none';
                    return;
                }

                const { from, to } = editor.state.selection;
                const start = editor.view.coordsAtPos(from);
                const end = editor.view.coordsAtPos(to);

                const rect = {
                    left: Math.min(start.left, end.left),
                    right: Math.max(start.right, end.right),
                    top: Math.min(start.top, end.top),
                    bottom: Math.max(start.bottom, end.bottom),
                };

                bubbleMenu.style.display = 'flex';
                bubbleMenu.style.position = 'absolute';
                bubbleMenu.style.left = `${rect.left + (rect.right - rect.left) / 2 - bubbleMenu.offsetWidth / 2}px`;
                bubbleMenu.style.top = `${rect.top - bubbleMenu.offsetHeight - 10}px`;
                bubbleMenu.style.zIndex = '1000';

                // 更新浮动菜单按钮状态
                updateBubbleMenuStates(bubbleMenu);
            });

            // 点击其他地方隐藏菜单
            document.addEventListener('click', (e) => {
                if (!bubbleMenu.contains(e.target) && !editor.view.dom.contains(e.target)) {
                    hideTimeout = setTimeout(() => {
                        bubbleMenu.style.display = 'none';
                    }, 100);
                }
            });
        }

        // 处理浮动菜单动作
        function handleBubbleMenuAction(action) {
            switch (action) {
                case 'bold':
                    editor.chain().focus().toggleBold().run();
                    break;
                case 'italic':
                    editor.chain().focus().toggleItalic().run();
                    break;
                case 'underline':
                    editor.chain().focus().toggleUnderline().run();
                    break;
                case 'highlight':
                    editor.chain().focus().toggleHighlight().run();
                    break;
                case 'link':
                    const url = prompt('请输入链接地址:');
                    if (url) {
                        editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
                    }
                    break;
            }
        }

        // 更新浮动菜单状态
        function updateBubbleMenuStates(bubbleMenu) {
            const buttons = bubbleMenu.querySelectorAll('button');
            buttons.forEach(button => {
                const action = button.dataset.action;
                const isActive = editor.isActive(action);
                button.classList.toggle('active', isActive);
            });
        }

        // 初始化工具栏事件
        function initToolbarEvents() {
            // 撤销/重做
            document.getElementById('undo').addEventListener('click', () => {
                editor.chain().focus().undo().run();
            });

            document.getElementById('redo').addEventListener('click', () => {
                editor.chain().focus().redo().run();
            });

            // 标题选择
            document.getElementById('heading').addEventListener('change', (e) => {
                const level = e.target.value;
                if (level === 'paragraph') {
                    editor.chain().focus().setParagraph().run();
                } else {
                    editor.chain().focus().toggleHeading({ level: parseInt(level) }).run();
                }
            });

            // 文本格式化
            document.getElementById('bold').addEventListener('click', () => {
                editor.chain().focus().toggleBold().run();
            });

            document.getElementById('italic').addEventListener('click', () => {
                editor.chain().focus().toggleItalic().run();
            });

            document.getElementById('underline').addEventListener('click', () => {
                editor.chain().focus().toggleUnderline().run();
            });

            document.getElementById('strike').addEventListener('click', () => {
                editor.chain().focus().toggleStrike().run();
            });

            // 高亮和代码
            document.getElementById('highlight').addEventListener('click', () => {
                editor.chain().focus().toggleHighlight().run();
            });

            document.getElementById('code').addEventListener('click', () => {
                editor.chain().focus().toggleCode().run();
            });

            // 列表
            document.getElementById('bulletList').addEventListener('click', () => {
                editor.chain().focus().toggleBulletList().run();
            });

            document.getElementById('orderedList').addEventListener('click', () => {
                editor.chain().focus().toggleOrderedList().run();
            });

            document.getElementById('taskList').addEventListener('click', () => {
                editor.chain().focus().toggleTaskList().run();
            });

            // 其他格式
            document.getElementById('blockquote').addEventListener('click', () => {
                editor.chain().focus().toggleBlockquote().run();
            });

            document.getElementById('codeBlock').addEventListener('click', () => {
                editor.chain().focus().toggleCodeBlock().run();
            });

            document.getElementById('horizontalRule').addEventListener('click', () => {
                editor.chain().focus().setHorizontalRule().run();
            });

            // 链接和图片
            document.getElementById('link').addEventListener('click', () => {
                const url = prompt('请输入链接地址:');
                if (url) {
                    editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
                }
            });

            document.getElementById('image').addEventListener('click', () => {
                const url = prompt('请输入图片地址:');
                if (url) {
                    editor.chain().focus().setImage({ src: url }).run();
                }
            });

            // 文本对齐
            let alignIndex = 0;
            const alignments = ['left', 'center', 'right', 'justify'];
            document.getElementById('textAlign').addEventListener('click', () => {
                alignIndex = (alignIndex + 1) % alignments.length;
                editor.chain().focus().setTextAlign(alignments[alignIndex]).run();
            });
        }

        // 更新工具栏状态
        function updateToolbarStates() {
            // 更新按钮激活状态
            const buttons = {
                'bold': editor.isActive('bold'),
                'italic': editor.isActive('italic'),
                'underline': editor.isActive('underline'),
                'strike': editor.isActive('strike'),
                'highlight': editor.isActive('highlight'),
                'code': editor.isActive('code'),
                'bulletList': editor.isActive('bulletList'),
                'orderedList': editor.isActive('orderedList'),
                'taskList': editor.isActive('taskList'),
                'blockquote': editor.isActive('blockquote'),
                'codeBlock': editor.isActive('codeBlock'),
                'link': editor.isActive('link'),
            };

            Object.keys(buttons).forEach(id => {
                const button = document.getElementById(id);
                if (button) {
                    button.classList.toggle('active', buttons[id]);
                }
            });

            // 更新标题选择器
            const headingSelect = document.getElementById('heading');
            if (editor.isActive('heading', { level: 1 })) {
                headingSelect.value = '1';
            } else if (editor.isActive('heading', { level: 2 })) {
                headingSelect.value = '2';
            } else if (editor.isActive('heading', { level: 3 })) {
                headingSelect.value = '3';
            } else {
                headingSelect.value = 'paragraph';
            }
        }

        // 更新 UI 状态
        function updateUI() {
            // 更新撤销/重做按钮状态
            document.getElementById('undo').disabled = !editor.can().undo();
            document.getElementById('redo').disabled = !editor.can().redo();

            updateToolbarStates();
        }

        // 更新统计信息
        function updateStats() {
            const stats = editor.storage.characterCount;
            document.getElementById('charCount').textContent = stats.characters();
            document.getElementById('wordCount').textContent = stats.words();

            // 计算段落数
            const doc = editor.state.doc;
            let paragraphCount = 0;
            doc.descendants((node) => {
                if (node.type.name === 'paragraph') {
                    paragraphCount++;
                }
            });
            document.getElementById('paragraphCount').textContent = paragraphCount;
        }

        // 演示功能函数
        window.insertSampleContent = function() {
            const sampleContent = `
                <h2>🚀 Tiptap 功能演示</h2>
                <p>这里展示了 Tiptap 的各种功能：</p>

                <h3>文本格式</h3>
                <p>支持 <strong>粗体</strong>、<em>斜体</em>、<u>下划线</u>、<s>删除线</s>、<mark>高亮</mark> 和 <code>行内代码</code>。</p>

                <h3>列表功能</h3>
                <ul>
                    <li>无序列表项 1</li>
                    <li>无序列表项 2
                        <ul>
                            <li>嵌套列表项</li>
                        </ul>
                    </li>
                </ul>

                <ol>
                    <li>有序列表项 1</li>
                    <li>有序列表项 2</li>
                </ol>

                <ul data-type="taskList">
                    <li data-type="taskItem" data-checked="true">已完成的任务</li>
                    <li data-type="taskItem" data-checked="false">待完成的任务</li>
                </ul>

                <h3>引用和代码</h3>
                <blockquote>
                    <p>这是一个引用块，用于突出显示重要内容。</p>
                </blockquote>

                <pre><code>// 这是一个代码块
function hello() {
    console.log('Hello, Tiptap!');
}</code></pre>

                <hr>

                <p>还可以插入 <a href="https://tiptap.dev">链接</a> 和图片。</p>
            `;

            editor.chain().focus().setContent(sampleContent).run();
        };

        window.getHTML = function() {
            const html = editor.getHTML();
            document.getElementById('output').textContent = html;
            console.log('HTML:', html);
        };

        window.getJSON = function() {
            const json = JSON.stringify(editor.getJSON(), null, 2);
            document.getElementById('output').textContent = json;
            console.log('JSON:', json);
        };

        window.clearContent = function() {
            editor.chain().focus().clearContent().run();
        };

        window.focusEditor = function() {
            editor.chain().focus().run();
        };

        window.insertTable = function() {
            editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
        };

        window.insertEmoji = function() {
            const emojis = ['😀', '😍', '🤔', '👍', '🎉', '🚀', '💡', '🔥'];
            const randomEmoji = emojis[Math.floor(Math.random() * emojis.length)];
            editor.chain().focus().insertContent(randomEmoji).run();
        };

        window.toggleReadOnly = function() {
            isReadOnly = !isReadOnly;
            editor.setEditable(!isReadOnly);

            const button = event.target;
            button.textContent = isReadOnly ? '切换编辑模式' : '切换只读模式';
            button.style.background = isReadOnly ? '#dc3545' : '#007aff';
        };

        window.selectAll = function() {
            editor.chain().focus().selectAll().run();
        };

        // 页面加载完成后初始化编辑器
        document.addEventListener('DOMContentLoaded', initEditor);
    </script>
</body>
</html>