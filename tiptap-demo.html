<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tiptap 富文本编辑器演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f7;
            color: #1d1d1f;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .editor-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .toolbar {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 16px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .toolbar-group {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .toolbar-group:not(:last-child)::after {
            content: '';
            width: 1px;
            height: 24px;
            background: #dee2e6;
            margin-left: 8px;
        }

        .toolbar button, .toolbar select {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .toolbar button:hover, .toolbar select:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }

        .toolbar button.active {
            background: #007aff;
            color: white;
            border-color: #007aff;
        }

        .toolbar button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .editor {
            min-height: 400px;
            padding: 24px;
            font-size: 16px;
            line-height: 1.6;
        }

        .editor:focus {
            outline: none;
        }

        /* Tiptap 编辑器样式 */
        .tiptap {
            outline: none;
        }

        .tiptap h1, .tiptap h2, .tiptap h3 {
            margin: 1.5em 0 0.5em 0;
            font-weight: 600;
        }

        .tiptap h1 { font-size: 2rem; }
        .tiptap h2 { font-size: 1.5rem; }
        .tiptap h3 { font-size: 1.25rem; }

        .tiptap p {
            margin: 0.75em 0;
        }

        .tiptap ul, .tiptap ol {
            margin: 0.75em 0;
            padding-left: 1.5em;
        }

        .tiptap li {
            margin: 0.25em 0;
        }

        .tiptap blockquote {
            border-left: 4px solid #007aff;
            padding-left: 1em;
            margin: 1em 0;
            font-style: italic;
            color: #666;
        }

        .tiptap code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 0.9em;
        }

        .tiptap pre {
            background: #f8f9fa;
            padding: 1em;
            border-radius: 8px;
            overflow-x: auto;
            margin: 1em 0;
        }

        .tiptap pre code {
            background: none;
            padding: 0;
        }

        .tiptap a {
            color: #007aff;
            text-decoration: none;
        }

        .tiptap a:hover {
            text-decoration: underline;
        }

        .tiptap img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 1em 0;
        }

        .tiptap hr {
            border: none;
            height: 2px;
            background: #e9ecef;
            margin: 2em 0;
            border-radius: 1px;
        }

        /* 浮动菜单样式 */
        .bubble-menu {
            display: flex;
            align-items: center;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            padding: 6px;
            gap: 2px;
            border: 1px solid rgba(0,0,0,0.08);
            backdrop-filter: blur(20px);
            z-index: 1000;
        }

        .bubble-menu button {
            padding: 8px 12px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.15s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            color: #374151;
            min-height: 32px;
        }

        .bubble-menu button:hover {
            background: #f3f4f6;
            color: #111827;
        }

        .bubble-menu button.active {
            background: #3b82f6;
            color: white;
        }

        .bubble-menu .ask-ai-btn {
            background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
            color: white;
            font-weight: 600;
        }

        .bubble-menu .ask-ai-btn:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #9333ea 100%);
            transform: translateY(-1px);
        }

        .bubble-menu .divider {
            width: 1px;
            height: 20px;
            background: #e5e7eb;
            margin: 0 4px;
        }

        .bubble-menu select {
            border: none;
            background: transparent;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.15s ease;
        }

        .bubble-menu select:hover {
            background: #f3f4f6;
        }

        /* AI 菜单样式 */
        .ai-menu {
            position: absolute;
            background: white;
            border-radius: 12px;
            box-shadow: 0 12px 48px rgba(0,0,0,0.15);
            border: 1px solid rgba(0,0,0,0.08);
            padding: 16px;
            min-width: 280px;
            z-index: 1001;
            display: none;
        }

        .ai-menu.show {
            display: block;
            animation: aiMenuSlideIn 0.2s ease-out;
        }

        @keyframes aiMenuSlideIn {
            from {
                opacity: 0;
                transform: translateY(-8px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .ai-search-box {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 16px;
            transition: all 0.15s ease;
        }

        .ai-search-box:focus {
            outline: none;
            border-color: #8b5cf6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }

        .ai-section {
            margin-bottom: 16px;
        }

        .ai-section:last-child {
            margin-bottom: 0;
        }

        .ai-section-title {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }

        .ai-option {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 10px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.15s ease;
            margin-bottom: 4px;
        }

        .ai-option:hover {
            background: #f3f4f6;
        }

        .ai-option-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .ai-option-text {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
        }

        /* 颜色选择器样式 */
        .color-picker {
            position: absolute;
            background: white;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12);
            border: 1px solid rgba(0,0,0,0.08);
            padding: 12px;
            display: none;
            z-index: 1002;
        }

        .color-picker.show {
            display: block;
        }

        .color-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 6px;
        }

        .color-option {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.15s ease;
        }

        .color-option:hover {
            transform: scale(1.1);
            border-color: #374151;
        }

        /* 统计信息 */
        .stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
        }

        .stats-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 功能演示区域 */
        .demo-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .demo-section h3 {
            margin-bottom: 16px;
            color: #1d1d1f;
            font-size: 1.25rem;
        }

        .demo-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .demo-button {
            padding: 10px 16px;
            background: #007aff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .demo-button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .demo-button:active {
            transform: translateY(0);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .toolbar {
                padding: 12px;
            }

            .editor {
                padding: 16px;
            }

            .stats {
                flex-direction: column;
                gap: 8px;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Tiptap 富文本编辑器</h1>
            <p>体验现代化的无头富文本编辑器框架</p>
        </div>

        <div class="editor-container">
            <div class="toolbar">
                <div class="toolbar-group">
                    <button id="undo" title="撤销">↶</button>
                    <button id="redo" title="重做">↷</button>
                </div>

                <div class="toolbar-group">
                    <select id="heading">
                        <option value="paragraph">正文</option>
                        <option value="1">标题 1</option>
                        <option value="2">标题 2</option>
                        <option value="3">标题 3</option>
                    </select>
                </div>

                <div class="toolbar-group">
                    <button id="bold" title="粗体"><strong>B</strong></button>
                    <button id="italic" title="斜体"><em>I</em></button>
                    <button id="underline" title="下划线"><u>U</u></button>
                    <button id="strike" title="删除线"><s>S</s></button>
                </div>

                <div class="toolbar-group">
                    <button id="highlight" title="高亮">🖍️</button>
                    <button id="code" title="行内代码">&lt;/&gt;</button>
                </div>

                <div class="toolbar-group">
                    <button id="bulletList" title="无序列表">• 列表</button>
                    <button id="orderedList" title="有序列表">1. 列表</button>
                    <button id="taskList" title="任务列表">☑️ 任务</button>
                </div>

                <div class="toolbar-group">
                    <button id="blockquote" title="引用">❝ 引用</button>
                    <button id="codeBlock" title="代码块">{ } 代码</button>
                    <button id="horizontalRule" title="分割线">━━━</button>
                </div>

                <div class="toolbar-group">
                    <button id="link" title="链接">🔗</button>
                    <button id="image" title="图片">🖼️</button>
                </div>

                <div class="toolbar-group">
                    <button id="textAlign" title="文本对齐">⚏</button>
                </div>
            </div>

            <div id="editor" class="editor"></div>

            <div class="stats">
                <div class="stats-item">
                    <span>字符数：</span>
                    <span id="charCount">0</span>
                </div>
                <div class="stats-item">
                    <span>单词数：</span>
                    <span id="wordCount">0</span>
                </div>
                <div class="stats-item">
                    <span>段落数：</span>
                    <span id="paragraphCount">0</span>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>📝 内容操作演示</h3>
            <div class="demo-buttons">
                <button class="demo-button" onclick="insertSampleContent()">插入示例内容</button>
                <button class="demo-button" onclick="getHTML()">获取 HTML</button>
                <button class="demo-button" onclick="getJSON()">获取 JSON</button>
                <button class="demo-button" onclick="clearContent()">清空内容</button>
                <button class="demo-button" onclick="focusEditor()">聚焦编辑器</button>
            </div>
        </div>

        <div class="demo-section">
            <h3>🎯 格式化演示</h3>
            <div class="demo-buttons">
                <button class="demo-button" onclick="insertTable()">插入表格</button>
                <button class="demo-button" onclick="insertEmoji()">插入表情</button>
                <button class="demo-button" onclick="toggleReadOnly()">切换只读模式</button>
                <button class="demo-button" onclick="selectAll()">全选内容</button>
            </div>
        </div>

        <div class="demo-section">
            <h3>📊 输出预览</h3>
            <div id="output" style="background: #f8f9fa; padding: 16px; border-radius: 8px; font-family: monospace; font-size: 14px; white-space: pre-wrap; max-height: 200px; overflow-y: auto;"></div>
        </div>
    </div>

    <!-- 使用 CDN 引入 Tiptap -->
    <script type="module">
        import { Editor } from 'https://esm.sh/@tiptap/core@2.1.13'
        import StarterKit from 'https://esm.sh/@tiptap/starter-kit@2.1.13'
        import Highlight from 'https://esm.sh/@tiptap/extension-highlight@2.1.13'
        import Underline from 'https://esm.sh/@tiptap/extension-underline@2.1.13'
        import TextAlign from 'https://esm.sh/@tiptap/extension-text-align@2.1.13'
        import Link from 'https://esm.sh/@tiptap/extension-link@2.1.13'
        import Image from 'https://esm.sh/@tiptap/extension-image@2.1.13'
        import TaskList from 'https://esm.sh/@tiptap/extension-task-list@2.1.13'
        import TaskItem from 'https://esm.sh/@tiptap/extension-task-item@2.1.13'
        import CharacterCount from 'https://esm.sh/@tiptap/extension-character-count@2.1.13'
        import Placeholder from 'https://esm.sh/@tiptap/extension-placeholder@2.1.13'
        // import Color from 'https://esm.sh/@tiptap/extension-color@2.1.13'
        // import TextStyle from 'https://esm.sh/@tiptap/extension-text-style@2.1.13'

        // 全局变量
        let editor;
        let isReadOnly = false;

        // 初始化编辑器
        window.initEditor = function initEditor() {
            console.log('开始初始化编辑器...');

            const editorElement = document.querySelector('#editor');
            if (!editorElement) {
                console.error('找不到编辑器元素');
                return;
            }

            editor = new Editor({
                element: document.querySelector('#editor'),
                extensions: [
                    StarterKit.configure({
                        history: {
                            depth: 100,
                        },
                    }),
                    Highlight.configure({
                        multicolor: true,
                    }),
                    Underline,
                    TextAlign.configure({
                        types: ['heading', 'paragraph'],
                    }),
                    Link.configure({
                        openOnClick: false,
                        HTMLAttributes: {
                            class: 'tiptap-link',
                        },
                    }),
                    Image.configure({
                        inline: true,
                        allowBase64: true,
                    }),
                    TaskList,
                    TaskItem.configure({
                        nested: true,
                    }),
                    CharacterCount,
                    Placeholder.configure({
                        placeholder: '开始输入你的内容...',
                    }),
                    // TextStyle,
                    // Color,
                ],
                content: `
                    <h1>欢迎使用 Tiptap 编辑器！</h1>
                    <p>这是一个功能强大的<strong>富文本编辑器</strong>演示。你可以：</p>
                    <ul>
                        <li>使用各种<em>文本格式</em></li>
                        <li>插入<a href="https://tiptap.dev">链接</a></li>
                        <li>添加<mark>高亮文本</mark></li>
                        <li>创建代码块和<code>行内代码</code></li>
                    </ul>
                    <blockquote>
                        <p>Tiptap 是一个无头的富文本编辑器框架，基于 ProseMirror 构建。</p>
                    </blockquote>
                    <p>试试上面的工具栏按钮，或者选择文本查看浮动菜单！</p>
                `,
                onTransaction: () => {
                    updateUI();
                    updateStats();
                },
                onSelectionUpdate: ({ editor }) => {
                    updateToolbarStates();
                },
            });

            // 创建浮动菜单
            createBubbleMenu();

            // 初始化工具栏事件
            initToolbarEvents();

            // 初始化 UI 状态
            updateUI();
            updateStats();

            console.log('编辑器初始化完成，editor对象:', editor);
        }

        // 创建浮动菜单
        window.createBubbleMenu = function createBubbleMenu() {
            const bubbleMenu = document.createElement('div');
            bubbleMenu.className = 'bubble-menu';
            bubbleMenu.innerHTML = `
                <button class="ask-ai-btn" data-action="ask-ai" title="Ask AI">
                    <span>✨</span>
                    <span>Ask AI</span>
                </button>
                <div class="divider"></div>
                <select data-action="list-type" title="列表类型">
                    <option value="">Bullet List</option>
                    <option value="bulletList">• 无序列表</option>
                    <option value="orderedList">1. 有序列表</option>
                    <option value="taskList">☑️ 任务列表</option>
                </select>
                <div class="divider"></div>
                <button data-action="bold" title="粗体"><strong>B</strong></button>
                <button data-action="italic" title="斜体"><em>I</em></button>
                <button data-action="underline" title="下划线"><u>U</u></button>
                <button data-action="strike" title="删除线"><s>S</s></button>
                <button data-action="code" title="代码">&lt;/&gt;</button>
                <div class="divider"></div>
                <button data-action="color" title="文字颜色">
                    <span style="color: #3b82f6;">A</span>
                    <span>▼</span>
                </button>
                <button data-action="link" title="链接">🔗</button>
                <button data-action="more" title="更多选项">Σ</button>
            `;

            document.body.appendChild(bubbleMenu);

            // 创建 AI 菜单
            createAIMenu();

            // 创建颜色选择器
            createColorPicker();

            // 浮动菜单事件处理
            setupBubbleMenuEvents(bubbleMenu);
        }

        // 创建 AI 菜单
        window.createAIMenu = function createAIMenu() {
            const aiMenu = document.createElement('div');
            aiMenu.className = 'ai-menu';
            aiMenu.id = 'ai-menu';
            aiMenu.innerHTML = `
                <input type="text" class="ai-search-box" placeholder="Ask AI to edit or generate..." />

                <div class="ai-section">
                    <div class="ai-section-title">Edit or review selection</div>
                    <div class="ai-option" data-action="improve">
                        <div class="ai-option-icon">🔄</div>
                        <div class="ai-option-text">Improve writing</div>
                    </div>
                    <div class="ai-option" data-action="shorter">
                        <div class="ai-option-icon">📝</div>
                        <div class="ai-option-text">Make shorter</div>
                    </div>
                    <div class="ai-option" data-action="longer">
                        <div class="ai-option-icon">📄</div>
                        <div class="ai-option-text">Make longer</div>
                    </div>
                </div>

                <div class="ai-section">
                    <div class="ai-section-title">Use AI to do more</div>
                    <div class="ai-option" data-action="continue">
                        <div class="ai-option-icon">▶️</div>
                        <div class="ai-option-text">Continue writing</div>
                    </div>
                </div>
            `;

            document.body.appendChild(aiMenu);

            // AI 菜单事件处理
            setupAIMenuEvents(aiMenu);
        }

        // 创建颜色选择器
        window.createColorPicker = function createColorPicker() {
            const colorPicker = document.createElement('div');
            colorPicker.className = 'color-picker';
            colorPicker.id = 'color-picker';

            const colors = [
                '#000000', '#374151', '#6b7280', '#9ca3af',
                '#ef4444', '#f97316', '#f59e0b', '#eab308',
                '#22c55e', '#10b981', '#06b6d4', '#0ea5e9',
                '#3b82f6', '#6366f1', '#8b5cf6', '#a855f7',
                '#ec4899', '#f43f5e', '#ffffff', 'transparent'
            ];

            const colorGrid = document.createElement('div');
            colorGrid.className = 'color-grid';

            colors.forEach(color => {
                const colorOption = document.createElement('div');
                colorOption.className = 'color-option';
                colorOption.style.backgroundColor = color === 'transparent' ? '#f3f4f6' : color;
                colorOption.dataset.color = color;

                if (color === 'transparent') {
                    colorOption.innerHTML = '🚫';
                    colorOption.style.display = 'flex';
                    colorOption.style.alignItems = 'center';
                    colorOption.style.justifyContent = 'center';
                    colorOption.style.fontSize = '12px';
                }

                colorGrid.appendChild(colorOption);
            });

            colorPicker.appendChild(colorGrid);
            document.body.appendChild(colorPicker);

            // 颜色选择器事件处理
            setupColorPickerEvents(colorPicker);
        }

        // 设置浮动菜单事件
        window.setupBubbleMenuEvents = function setupBubbleMenuEvents(bubbleMenu) {
            bubbleMenu.addEventListener('click', (e) => {
                const button = e.target.closest('button');
                const select = e.target.closest('select');

                if (button) {
                    const action = button.dataset.action;
                    handleBubbleMenuAction(action, e);
                } else if (select) {
                    const action = select.dataset.action;
                    const value = select.value;
                    handleSelectAction(action, value);
                }
            });

            bubbleMenu.addEventListener('change', (e) => {
                if (e.target.tagName === 'SELECT') {
                    const action = e.target.dataset.action;
                    const value = e.target.value;
                    handleSelectAction(action, value);
                }
            });
        }

        // 设置 AI 菜单事件
        function setupAIMenuEvents(aiMenu) {
            aiMenu.addEventListener('click', (e) => {
                const option = e.target.closest('.ai-option');
                if (option) {
                    const action = option.dataset.action;
                    handleAIAction(action);
                    hideAIMenu();
                }
            });

            // 搜索框事件
            const searchBox = aiMenu.querySelector('.ai-search-box');
            searchBox.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const query = e.target.value.trim();
                    if (query) {
                        handleAIQuery(query);
                        hideAIMenu();
                    }
                }
            });
        }

        // 设置颜色选择器事件
        function setupColorPickerEvents(colorPicker) {
            colorPicker.addEventListener('click', (e) => {
                const colorOption = e.target.closest('.color-option');
                if (colorOption) {
                    const color = colorOption.dataset.color;
                    applyTextColor(color);
                    hideColorPicker();
                }
            });
        }

            // 监听选择变化来显示/隐藏浮动菜单
            let hideTimeout;
            editor.on('selectionUpdate', ({ editor }) => {
                clearTimeout(hideTimeout);

                if (editor.state.selection.empty) {
                    bubbleMenu.style.display = 'none';
                    hideAIMenu();
                    hideColorPicker();
                    return;
                }

                const { from, to } = editor.state.selection;
                const start = editor.view.coordsAtPos(from);
                const end = editor.view.coordsAtPos(to);

                const rect = {
                    left: Math.min(start.left, end.left),
                    right: Math.max(start.right, end.right),
                    top: Math.min(start.top, end.top),
                    bottom: Math.max(start.bottom, end.bottom),
                };

                bubbleMenu.style.display = 'flex';
                bubbleMenu.style.position = 'absolute';
                bubbleMenu.style.left = `${rect.left + (rect.right - rect.left) / 2 - bubbleMenu.offsetWidth / 2}px`;
                bubbleMenu.style.top = `${rect.top - bubbleMenu.offsetHeight - 10}px`;
                bubbleMenu.style.zIndex = '1000';

                // 更新浮动菜单按钮状态
                updateBubbleMenuStates(bubbleMenu);
            });

            // 点击其他地方隐藏菜单
            document.addEventListener('click', (e) => {
                if (!bubbleMenu.contains(e.target) &&
                    !document.getElementById('ai-menu').contains(e.target) &&
                    !document.getElementById('color-picker').contains(e.target) &&
                    !editor.view.dom.contains(e.target)) {
                    hideTimeout = setTimeout(() => {
                        bubbleMenu.style.display = 'none';
                        hideAIMenu();
                        hideColorPicker();
                    }, 100);
                }
            });
        }

        // 处理浮动菜单动作
        function handleBubbleMenuAction(action, event) {
            switch (action) {
                case 'ask-ai':
                    showAIMenu(event.target);
                    break;
                case 'bold':
                    editor.chain().focus().toggleBold().run();
                    break;
                case 'italic':
                    editor.chain().focus().toggleItalic().run();
                    break;
                case 'underline':
                    editor.chain().focus().toggleUnderline().run();
                    break;
                case 'strike':
                    editor.chain().focus().toggleStrike().run();
                    break;
                case 'code':
                    editor.chain().focus().toggleCode().run();
                    break;
                case 'color':
                    showColorPicker(event.target);
                    break;
                case 'link':
                    const url = prompt('请输入链接地址:');
                    if (url) {
                        editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
                    }
                    break;
                case 'more':
                    showMoreOptions(event.target);
                    break;
            }
        }

        // 处理选择器动作
        function handleSelectAction(action, value) {
            if (action === 'list-type' && value) {
                switch (value) {
                    case 'bulletList':
                        editor.chain().focus().toggleBulletList().run();
                        break;
                    case 'orderedList':
                        editor.chain().focus().toggleOrderedList().run();
                        break;
                    case 'taskList':
                        editor.chain().focus().toggleTaskList().run();
                        break;
                }
            }
        }

        // 显示 AI 菜单
        function showAIMenu(trigger) {
            const aiMenu = document.getElementById('ai-menu');
            const rect = trigger.getBoundingClientRect();

            aiMenu.style.left = `${rect.left}px`;
            aiMenu.style.top = `${rect.bottom + 8}px`;
            aiMenu.classList.add('show');

            // 聚焦搜索框
            setTimeout(() => {
                aiMenu.querySelector('.ai-search-box').focus();
            }, 100);
        }

        // 隐藏 AI 菜单
        function hideAIMenu() {
            const aiMenu = document.getElementById('ai-menu');
            aiMenu.classList.remove('show');
        }

        // 显示颜色选择器
        function showColorPicker(trigger) {
            const colorPicker = document.getElementById('color-picker');
            const rect = trigger.getBoundingClientRect();

            colorPicker.style.left = `${rect.left}px`;
            colorPicker.style.top = `${rect.bottom + 8}px`;
            colorPicker.classList.add('show');
        }

        // 隐藏颜色选择器
        function hideColorPicker() {
            const colorPicker = document.getElementById('color-picker');
            colorPicker.classList.remove('show');
        }

        // 应用文字颜色
        function applyTextColor(color) {
            if (color === 'transparent') {
                editor.chain().focus().unsetColor().run();
            } else {
                editor.chain().focus().setColor(color).run();
            }
        }

        // 处理 AI 动作
        function handleAIAction(action) {
            const { from, to } = editor.state.selection;
            const selectedText = editor.state.doc.textBetween(from, to);

            if (!selectedText.trim()) {
                alert('请先选择一些文本');
                return;
            }

            // 模拟 AI 处理
            let newText = '';

            switch (action) {
                case 'improve':
                    newText = improveText(selectedText);
                    break;
                case 'shorter':
                    newText = makeShorter(selectedText);
                    break;
                case 'longer':
                    newText = makeLonger(selectedText);
                    break;
                case 'continue':
                    newText = selectedText + ' ' + continueWriting(selectedText);
                    break;
            }

            if (newText) {
                editor.chain().focus().deleteRange({ from, to }).insertContent(newText).run();
            }
        }

        // 处理 AI 查询
        function handleAIQuery(query) {
            const { from, to } = editor.state.selection;
            const selectedText = editor.state.doc.textBetween(from, to);

            // 模拟 AI 响应
            const response = generateAIResponse(query, selectedText);

            if (selectedText.trim()) {
                editor.chain().focus().deleteRange({ from, to }).insertContent(response).run();
            } else {
                editor.chain().focus().insertContent(response).run();
            }
        }

        // 模拟 AI 功能
        function improveText(text) {
            // 简单的文本改进模拟
            return text
                .replace(/\b(good|nice|great)\b/gi, 'excellent')
                .replace(/\b(bad|poor)\b/gi, 'suboptimal')
                .replace(/\b(big|large)\b/gi, 'substantial')
                .replace(/\b(small|little)\b/gi, 'minimal');
        }

        function makeShorter(text) {
            // 简单的文本缩短模拟
            const sentences = text.split(/[.!?]+/).filter(s => s.trim());
            if (sentences.length > 1) {
                return sentences.slice(0, Math.ceil(sentences.length / 2)).join('. ') + '.';
            }
            const words = text.split(' ');
            return words.slice(0, Math.ceil(words.length * 0.7)).join(' ');
        }

        function makeLonger(text) {
            // 简单的文本扩展模拟
            const expansions = [
                ' This is particularly important to consider.',
                ' Furthermore, this aspect deserves additional attention.',
                ' It\'s worth noting that this has significant implications.',
                ' This point cannot be overstated in its importance.'
            ];
            const randomExpansion = expansions[Math.floor(Math.random() * expansions.length)];
            return text + randomExpansion;
        }

        function continueWriting(text) {
            // 简单的续写模拟
            const continuations = [
                'Additionally, we should consider the broader implications of this topic.',
                'Moving forward, it\'s essential to examine the various factors involved.',
                'This leads us to explore the underlying principles and mechanisms.',
                'Furthermore, recent developments have shed new light on this subject.'
            ];
            return continuations[Math.floor(Math.random() * continuations.length)];
        }

        function generateAIResponse(query, context) {
            // 简单的 AI 响应生成
            const responses = [
                `Based on your query "${query}", here's a thoughtful response that addresses your needs.`,
                `Regarding "${query}", this is an interesting perspective that merits further exploration.`,
                `In response to "${query}", consider the following comprehensive analysis.`,
                `Your question about "${query}" opens up several important considerations.`
            ];
            return responses[Math.floor(Math.random() * responses.length)];
        }

        // 显示更多选项
        function showMoreOptions(trigger) {
            const options = [
                'Insert Table',
                'Insert Image',
                'Insert Code Block',
                'Insert Quote',
                'Insert Divider'
            ];

            const option = prompt('选择操作:\n' + options.map((opt, i) => `${i + 1}. ${opt}`).join('\n'));
            const index = parseInt(option) - 1;

            if (index >= 0 && index < options.length) {
                switch (index) {
                    case 0:
                        editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
                        break;
                    case 1:
                        const url = prompt('请输入图片地址:');
                        if (url) editor.chain().focus().setImage({ src: url }).run();
                        break;
                    case 2:
                        editor.chain().focus().toggleCodeBlock().run();
                        break;
                    case 3:
                        editor.chain().focus().toggleBlockquote().run();
                        break;
                    case 4:
                        editor.chain().focus().setHorizontalRule().run();
                        break;
                }
            }
        }

        // 更新浮动菜单状态
        function updateBubbleMenuStates(bubbleMenu) {
            const buttons = bubbleMenu.querySelectorAll('button[data-action]');
            buttons.forEach(button => {
                const action = button.dataset.action;
                let isActive = false;

                switch (action) {
                    case 'bold':
                        isActive = editor.isActive('bold');
                        break;
                    case 'italic':
                        isActive = editor.isActive('italic');
                        break;
                    case 'underline':
                        isActive = editor.isActive('underline');
                        break;
                    case 'strike':
                        isActive = editor.isActive('strike');
                        break;
                    case 'code':
                        isActive = editor.isActive('code');
                        break;
                    case 'link':
                        isActive = editor.isActive('link');
                        break;
                }

                button.classList.toggle('active', isActive);
            });

            // 更新列表选择器
            const listSelect = bubbleMenu.querySelector('select[data-action="list-type"]');
            if (listSelect) {
                if (editor.isActive('bulletList')) {
                    listSelect.value = 'bulletList';
                } else if (editor.isActive('orderedList')) {
                    listSelect.value = 'orderedList';
                } else if (editor.isActive('taskList')) {
                    listSelect.value = 'taskList';
                } else {
                    listSelect.value = '';
                }
            }
        }

        // 初始化工具栏事件
        function initToolbarEvents() {
            // 撤销/重做
            document.getElementById('undo').addEventListener('click', () => {
                editor.chain().focus().undo().run();
            });

            document.getElementById('redo').addEventListener('click', () => {
                editor.chain().focus().redo().run();
            });

            // 标题选择
            document.getElementById('heading').addEventListener('change', (e) => {
                const level = e.target.value;
                if (level === 'paragraph') {
                    editor.chain().focus().setParagraph().run();
                } else {
                    editor.chain().focus().toggleHeading({ level: parseInt(level) }).run();
                }
            });

            // 文本格式化
            document.getElementById('bold').addEventListener('click', () => {
                editor.chain().focus().toggleBold().run();
            });

            document.getElementById('italic').addEventListener('click', () => {
                editor.chain().focus().toggleItalic().run();
            });

            document.getElementById('underline').addEventListener('click', () => {
                editor.chain().focus().toggleUnderline().run();
            });

            document.getElementById('strike').addEventListener('click', () => {
                editor.chain().focus().toggleStrike().run();
            });

            // 高亮和代码
            document.getElementById('highlight').addEventListener('click', () => {
                editor.chain().focus().toggleHighlight().run();
            });

            document.getElementById('code').addEventListener('click', () => {
                editor.chain().focus().toggleCode().run();
            });

            // 列表
            document.getElementById('bulletList').addEventListener('click', () => {
                editor.chain().focus().toggleBulletList().run();
            });

            document.getElementById('orderedList').addEventListener('click', () => {
                editor.chain().focus().toggleOrderedList().run();
            });

            document.getElementById('taskList').addEventListener('click', () => {
                editor.chain().focus().toggleTaskList().run();
            });

            // 其他格式
            document.getElementById('blockquote').addEventListener('click', () => {
                editor.chain().focus().toggleBlockquote().run();
            });

            document.getElementById('codeBlock').addEventListener('click', () => {
                editor.chain().focus().toggleCodeBlock().run();
            });

            document.getElementById('horizontalRule').addEventListener('click', () => {
                editor.chain().focus().setHorizontalRule().run();
            });

            // 链接和图片
            document.getElementById('link').addEventListener('click', () => {
                const url = prompt('请输入链接地址:');
                if (url) {
                    editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
                }
            });

            document.getElementById('image').addEventListener('click', () => {
                const url = prompt('请输入图片地址:');
                if (url) {
                    editor.chain().focus().setImage({ src: url }).run();
                }
            });

            // 文本对齐
            let alignIndex = 0;
            const alignments = ['left', 'center', 'right', 'justify'];
            document.getElementById('textAlign').addEventListener('click', () => {
                alignIndex = (alignIndex + 1) % alignments.length;
                editor.chain().focus().setTextAlign(alignments[alignIndex]).run();
            });
        }

        // 更新工具栏状态
        function updateToolbarStates() {
            // 更新按钮激活状态
            const buttons = {
                'bold': editor.isActive('bold'),
                'italic': editor.isActive('italic'),
                'underline': editor.isActive('underline'),
                'strike': editor.isActive('strike'),
                'highlight': editor.isActive('highlight'),
                'code': editor.isActive('code'),
                'bulletList': editor.isActive('bulletList'),
                'orderedList': editor.isActive('orderedList'),
                'taskList': editor.isActive('taskList'),
                'blockquote': editor.isActive('blockquote'),
                'codeBlock': editor.isActive('codeBlock'),
                'link': editor.isActive('link'),
            };

            Object.keys(buttons).forEach(id => {
                const button = document.getElementById(id);
                if (button) {
                    button.classList.toggle('active', buttons[id]);
                }
            });

            // 更新标题选择器
            const headingSelect = document.getElementById('heading');
            if (editor.isActive('heading', { level: 1 })) {
                headingSelect.value = '1';
            } else if (editor.isActive('heading', { level: 2 })) {
                headingSelect.value = '2';
            } else if (editor.isActive('heading', { level: 3 })) {
                headingSelect.value = '3';
            } else {
                headingSelect.value = 'paragraph';
            }
        }

        // 更新 UI 状态
        function updateUI() {
            // 更新撤销/重做按钮状态
            document.getElementById('undo').disabled = !editor.can().undo();
            document.getElementById('redo').disabled = !editor.can().redo();

            updateToolbarStates();
        }

        // 更新统计信息
        function updateStats() {
            const stats = editor.storage.characterCount;
            document.getElementById('charCount').textContent = stats.characters();
            document.getElementById('wordCount').textContent = stats.words();

            // 计算段落数
            const doc = editor.state.doc;
            let paragraphCount = 0;
            doc.descendants((node) => {
                if (node.type.name === 'paragraph') {
                    paragraphCount++;
                }
            });
            document.getElementById('paragraphCount').textContent = paragraphCount;
        }

        // 演示功能函数
        window.insertSampleContent = function() {
            const sampleContent = `
                <h2>🚀 Tiptap 功能演示</h2>
                <p>这里展示了 Tiptap 的各种功能：</p>

                <h3>文本格式</h3>
                <p>支持 <strong>粗体</strong>、<em>斜体</em>、<u>下划线</u>、<s>删除线</s>、<mark>高亮</mark> 和 <code>行内代码</code>。</p>

                <h3>列表功能</h3>
                <ul>
                    <li>无序列表项 1</li>
                    <li>无序列表项 2
                        <ul>
                            <li>嵌套列表项</li>
                        </ul>
                    </li>
                </ul>

                <ol>
                    <li>有序列表项 1</li>
                    <li>有序列表项 2</li>
                </ol>

                <ul data-type="taskList">
                    <li data-type="taskItem" data-checked="true">已完成的任务</li>
                    <li data-type="taskItem" data-checked="false">待完成的任务</li>
                </ul>

                <h3>引用和代码</h3>
                <blockquote>
                    <p>这是一个引用块，用于突出显示重要内容。</p>
                </blockquote>

                <pre><code>// 这是一个代码块
function hello() {
    console.log('Hello, Tiptap!');
}</code></pre>

                <hr>

                <p>还可以插入 <a href="https://tiptap.dev">链接</a> 和图片。</p>
            `;

            editor.chain().focus().setContent(sampleContent).run();
        };

        window.getHTML = function() {
            const html = editor.getHTML();
            document.getElementById('output').textContent = html;
            console.log('HTML:', html);
        };

        window.getJSON = function() {
            const json = JSON.stringify(editor.getJSON(), null, 2);
            document.getElementById('output').textContent = json;
            console.log('JSON:', json);
        };

        window.clearContent = function() {
            editor.chain().focus().clearContent().run();
        };

        window.focusEditor = function() {
            editor.chain().focus().run();
        };

        window.insertTable = function() {
            editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
        };

        window.insertEmoji = function() {
            const emojis = ['😀', '😍', '🤔', '👍', '🎉', '🚀', '💡', '🔥'];
            const randomEmoji = emojis[Math.floor(Math.random() * emojis.length)];
            editor.chain().focus().insertContent(randomEmoji).run();
        };

        window.toggleReadOnly = function() {
            isReadOnly = !isReadOnly;
            editor.setEditable(!isReadOnly);

            const button = event.target;
            button.textContent = isReadOnly ? '切换编辑模式' : '切换只读模式';
            button.style.background = isReadOnly ? '#dc3545' : '#007aff';
        };

        window.selectAll = function() {
            editor.chain().focus().selectAll().run();
        };

        // 页面加载完成后初始化编辑器
        document.addEventListener('DOMContentLoaded', () => {
            try {
                initEditor();
                console.log('编辑器初始化成功');
            } catch (error) {
                console.error('编辑器初始化失败:', error);
            }
        });
    </script>
</body>
</html>